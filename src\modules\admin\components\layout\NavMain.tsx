'use client'

import {
  SidebarGroup,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar'
import {
  BarChart2,
  Calendar,
  CalendarDays,
  Home,
  LoaderIcon,
  PlusCircle,
  Settings,
} from 'lucide-react'

import { usePathname, useRouter } from 'next/navigation'
import { useState } from 'react'

const projects = [
  {
    name: 'Dashboard',
    url: '/admin/dashboard',
    icon: Home,
  },
  {
    name: 'Tạo booking page',
    url: '/admin/create-booking-page',
    icon: PlusCircle,
  },
  {
    name: 'Quản lý booking page',
    url: '/admin/manage-booking-pages',
    icon: Calendar,
  },
  {
    name: 'Quản lý đặt lịch',
    url: '/admin/bookings',
    icon: CalendarDays,
  },
  {
    name: 'Thống kê booking',
    url: '/admin/booking-stats',
    icon: BarChart2,
  },
  {
    name: 'Cài đặt',
    url: '/admin/settings',
    icon: Settings,
  },
]

export function NavProjects() {
  const pathname = usePathname()
  const router = useRouter()
  const [clickedItem, setClickedItem] = useState<string | null>(null)

  // Handle menu item click
  const handleMenuClick = (url: string) => {
    setClickedItem(url)
    router.push(url)
  }

  return (
    <SidebarGroup>
      <SidebarMenu>
        {projects.map((project) => {
          // Check if current path starts with the project URL or if this item was clicked
          const isPathActive = pathname.startsWith(project.url)
          const isClicked = clickedItem === project.url
          const isActive = isPathActive || isClicked

          const isActiveNext = isActive && isPathActive

          // If the path is active, clear the clicked state
          if (isPathActive && clickedItem === project.url) {
            setClickedItem(null)
          }

          return (
            <SidebarMenuItem key={project.name}>
              <SidebarMenuButton asChild size="lg" className={`${(isActiveNext) ? 'bg-primary/10' : ''}`}>
                <div
                  role="button"
                  tabIndex={0}
                  onClick={() => handleMenuClick(project.url)}
                  className="flex items-center gap-3 rounded-lg cursor-pointer transition-all duration-200 relative"
                >
                  {isActive && (
                    <div className="absolute left-0 top-0 bottom-0 w-1 bg-primary rounded-full"></div>
                  )}
                  <div
                    className={`transition-all duration-300 ${
                      isActive ? 'text-primary transform' : 'text-muted-foreground'
                    }`}
                  >
                    {
                      isClicked && !isPathActive
                        ? <LoaderIcon size={24} className="animate-spin" />
                        : (
                            <project.icon size={24} />
                          )
                    }

                  </div>
                  <span className={`transition-all duration-300 ${isActive ? 'font-semibold text-primary' : ''}`}>
                    {project.name}
                  </span>
                </div>
              </SidebarMenuButton>
            </SidebarMenuItem>
          )
        })}
      </SidebarMenu>
    </SidebarGroup>
  )
}

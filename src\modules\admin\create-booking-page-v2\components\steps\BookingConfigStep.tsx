'use client'

import React from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { 
  Settings, 
  Clock, 
  Users, 
  Plus, 
  Trash2, 
  Image as ImageIcon 
} from 'lucide-react'
import { useCreateBookingPageV2Store } from '../../stores/create-booking-page-v2.store'
import { FieldType } from '../../types'

const FIELD_TYPES: { value: FieldType; label: string }[] = [
  { value: 'football', label: 'Bóng đá' },
  { value: 'tennis', label: 'Tennis' },
  { value: 'badminton', label: 'Cầu lông' },
  { value: 'basketball', label: 'Bóng rổ' }
]

export const BookingConfigStep: React.FC = () => {
  const {
    bookingConfig,
    updateBookingConfig,
    add<PERSON>ield,
    remove<PERSON><PERSON>,
    update<PERSON><PERSON>,
    errors
  } = useCreateBookingPageV2Store()

  return (
    <div className="animate-in fade-in slide-in-from-right-4 duration-300">
      <Card className="max-w-5xl mx-auto bg-white/90 backdrop-blur-md border-orange-200/50 shadow-2xl">
        <CardHeader className="text-center px-6 sm:px-8 lg:px-12 py-8">
          <CardTitle className="text-2xl sm:text-3xl lg:text-4xl text-gray-900 flex items-center justify-center gap-3 mb-4">
            <Settings className="w-7 h-7 lg:w-8 lg:h-8 text-orange-500" />
            Cấu hình trang đặt chỗ
          </CardTitle>
          <p className="text-base sm:text-lg text-gray-600 max-w-2xl mx-auto">
            Thiết lập thông tin cơ bản để trang đặt chỗ có thể hoạt động
          </p>
        </CardHeader>
        
        <CardContent className="px-6 sm:px-8 lg:px-12 pb-8 space-y-8">
          {/* Banner Configuration */}
          <div className="space-y-6">
            <h3 className="text-lg lg:text-xl font-semibold text-gray-900 flex items-center gap-3">
              <ImageIcon className="w-6 h-6 text-orange-500" />
              Thông tin banner
            </h3>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="space-y-3">
                <Label htmlFor="bannerTitle" className="text-base font-semibold text-gray-800">
                  Tiêu đề banner *
                </Label>
                <Input
                  id="bannerTitle"
                  value={bookingConfig.bannerTitle}
                  onChange={(e) => updateBookingConfig({ bannerTitle: e.target.value })}
                  placeholder="Đặt sân thể thao ABC"
                  className={`h-12 text-base border-orange-200 focus:border-orange-400 focus:ring-orange-400 focus:ring-2 ${
                    errors.bannerTitle ? 'border-red-400 focus:border-red-400 focus:ring-red-400' : ''
                  }`}
                />
                {errors.bannerTitle && (
                  <p className="text-sm text-red-600">{errors.bannerTitle}</p>
                )}
              </div>
              
              <div className="space-y-3">
                <Label htmlFor="bannerSubtitle" className="text-base font-semibold text-gray-800">
                  Mô tả ngắn
                </Label>
                <Input
                  id="bannerSubtitle"
                  value={bookingConfig.bannerSubtitle}
                  onChange={(e) => updateBookingConfig({ bannerSubtitle: e.target.value })}
                  placeholder="Chọn sân và thời gian phù hợp với bạn"
                  className={`h-12 text-base border-orange-200 focus:border-orange-400 focus:ring-orange-400 focus:ring-2 ${
                    errors.bannerSubtitle ? 'border-red-400 focus:border-red-400 focus:ring-red-400' : ''
                  }`}
                />
                {errors.bannerSubtitle && (
                  <p className="text-sm text-red-600">{errors.bannerSubtitle}</p>
                )}
              </div>
            </div>
            
            <div className="space-y-3">
              <Label htmlFor="bannerImage" className="text-base font-semibold text-gray-800">
                URL hình ảnh banner
              </Label>
              <Input
                id="bannerImage"
                value={bookingConfig.bannerImage}
                onChange={(e) => updateBookingConfig({ bannerImage: e.target.value })}
                placeholder="https://example.com/banner.jpg"
                className={`h-12 text-base border-orange-200 focus:border-orange-400 focus:ring-orange-400 focus:ring-2 ${
                  errors.bannerImage ? 'border-red-400 focus:border-red-400 focus:ring-red-400' : ''
                }`}
              />
              {errors.bannerImage && (
                <p className="text-sm text-red-600">{errors.bannerImage}</p>
              )}
              <p className="text-sm text-gray-500">
                Hỗ trợ định dạng: JPG, JPEG, PNG, GIF, WebP
              </p>
            </div>
          </div>

          <Separator className="bg-orange-200" />

          {/* Operating Hours */}
          <div className="space-y-6">
            <h3 className="text-lg lg:text-xl font-semibold text-gray-900 flex items-center gap-3">
              <Clock className="w-6 h-6 text-orange-500" />
              Giờ hoạt động
            </h3>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="space-y-3">
                <Label htmlFor="openTime" className="text-base font-semibold text-gray-800">
                  Giờ mở cửa *
                </Label>
                <Input
                  id="openTime"
                  type="time"
                  value={bookingConfig.openTime}
                  onChange={(e) => updateBookingConfig({ openTime: e.target.value })}
                  className={`h-12 text-base border-orange-200 focus:border-orange-400 focus:ring-orange-400 focus:ring-2 ${
                    errors.openTime ? 'border-red-400 focus:border-red-400 focus:ring-red-400' : ''
                  }`}
                />
                {errors.openTime && (
                  <p className="text-sm text-red-600">{errors.openTime}</p>
                )}
              </div>
              
              <div className="space-y-3">
                <Label htmlFor="closeTime" className="text-base font-semibold text-gray-800">
                  Giờ đóng cửa *
                </Label>
                <Input
                  id="closeTime"
                  type="time"
                  value={bookingConfig.closeTime}
                  onChange={(e) => updateBookingConfig({ closeTime: e.target.value })}
                  className={`h-12 text-base border-orange-200 focus:border-orange-400 focus:ring-orange-400 focus:ring-2 ${
                    errors.closeTime ? 'border-red-400 focus:border-red-400 focus:ring-red-400' : ''
                  }`}
                />
                {errors.closeTime && (
                  <p className="text-sm text-red-600">{errors.closeTime}</p>
                )}
              </div>
            </div>
          </div>

          <Separator className="bg-orange-200" />

          {/* Fields Configuration */}
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg lg:text-xl font-semibold text-gray-900 flex items-center gap-3">
                <Users className="w-6 h-6 text-orange-500" />
                Danh sách sân ({bookingConfig.fields.length})
              </h3>
              <Button
                onClick={addField}
                variant="outline"
                size="sm"
                className="border-orange-300 text-orange-600 hover:bg-orange-50 h-10 px-4"
              >
                <Plus className="w-4 h-4 mr-2" />
                Thêm sân
              </Button>
            </div>

            {errors.fields && (
              <p className="text-sm text-red-600">{errors.fields}</p>
            )}

            {bookingConfig.fields.length === 0 ? (
              <div className="text-center py-12 text-gray-500 border-2 border-dashed border-gray-300 rounded-xl">
                <Users className="w-16 h-16 mx-auto mb-4 opacity-50" />
                <p className="text-lg font-medium mb-2">Chưa có sân nào</p>
                <p className="text-sm">Nhấn "Thêm sân" để bắt đầu thêm sân đầu tiên</p>
              </div>
            ) : (
              <div className="space-y-4">
                {bookingConfig.fields.map((field, index) => (
                  <div
                    key={field.id}
                    className="p-6 border border-orange-200 rounded-xl bg-gradient-to-r from-orange-50/50 to-orange-100/50"
                  >
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 items-end">
                      <div className="space-y-2">
                        <Label className="text-sm font-semibold text-gray-800">
                          Tên sân *
                        </Label>
                        <Input
                          value={field.name}
                          onChange={(e) => updateField(field.id, { name: e.target.value })}
                          placeholder={`Sân ${index + 1}`}
                          className="h-10 border-orange-200 focus:border-orange-400 focus:ring-orange-400 focus:ring-1"
                        />
                      </div>
                      
                      <div className="space-y-2">
                        <Label className="text-sm font-semibold text-gray-800">
                          Loại sân
                        </Label>
                        <select
                          value={field.type}
                          onChange={(e) => updateField(field.id, { type: e.target.value as FieldType })}
                          className="w-full h-10 px-3 py-2 border border-orange-200 rounded-md focus:border-orange-400 focus:ring-orange-400 focus:ring-1 bg-white"
                        >
                          {FIELD_TYPES.map(type => (
                            <option key={type.value} value={type.value}>
                              {type.label}
                            </option>
                          ))}
                        </select>
                      </div>
                      
                      <div className="space-y-2">
                        <Label className="text-sm font-semibold text-gray-800">
                          Sức chứa
                        </Label>
                        <Input
                          type="number"
                          min="1"
                          max="100"
                          value={field.capacity}
                          onChange={(e) => updateField(field.id, { capacity: parseInt(e.target.value) || 1 })}
                          className="h-10 border-orange-200 focus:border-orange-400 focus:ring-orange-400 focus:ring-1"
                        />
                      </div>
                      
                      <div className="flex justify-end">
                        <Button
                          onClick={() => removeField(field.id)}
                          variant="outline"
                          size="sm"
                          className="border-red-300 text-red-600 hover:bg-red-50 h-10 w-10 p-0"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

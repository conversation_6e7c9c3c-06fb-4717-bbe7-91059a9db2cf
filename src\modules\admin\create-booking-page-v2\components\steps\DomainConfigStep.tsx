'use client'

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Env } from '@/libs/Env'
import { Globe } from 'lucide-react'
import React from 'react'
import { useCreateBookingPageV2Store } from '../../stores/create-booking-page-v2.store'

export const DomainConfigStep: React.FC = () => {
  const {
    domainConfig,
    updateDomainConfig,
    errors,
  } = useCreateBookingPageV2Store()

  return (
    <div className="animate-in fade-in slide-in-from-right-4 duration-300">
      <div className="max-w-4xl mx-auto">
        <Card className="bg-white/90 backdrop-blur-md border-orange-200/50 shadow-2xl">
          <CardHeader className="text-center px-6 sm:px-8 lg:px-12 py-8">
            <CardTitle className="text-2xl sm:text-3xl lg:text-4xl text-gray-900 flex items-center justify-center gap-3 mb-4">
              <Globe className="w-7 h-7 lg:w-8 lg:h-8 text-orange-500" />
              Chọn tên miền
            </CardTitle>
            <p className="text-base sm:text-lg text-gray-600 max-w-2xl mx-auto">
              Chọn địa chỉ web để khách hàng có thể truy cập trang đặt chỗ của bạn
            </p>
          </CardHeader>

          <CardContent className="px-6 sm:px-8 lg:px-12 pb-8 space-y-8">
            <div className="space-y-6">
              {/* Subdomain Field */}
              <div className="space-y-3">
                <Label htmlFor="subdomain" className="text-base font-semibold text-gray-800">
                  Tên miền *
                </Label>
                <div className="bg-gradient-to-r from-orange-50 to-orange-100 border border-orange-200 rounded-xl h-fit  flex flex-col sm:flex-row sm:items-center gap-2 p-1">
                  <span className="font-medium text-base whitespace-nowrap pl-3">
                    {Env.NEXT_PUBLIC_DOMAIN}
                    /
                  </span>
                  <Input
                    id="subdomain"
                    value={domainConfig.subdomain}
                    onChange={e => updateDomainConfig({ subdomain: e.target.value.toLowerCase() })}
                    placeholder="ten-san-cua-ban"
                    className={`bg-white border-0 flex-1 h-12 text-base  focus:border-orange-400 focus:ring-orange-400 focus:ring-2 ${
                      errors.subdomain ? 'border-red-400 focus:border-red-400 focus:ring-red-400' : ''
                    }`}
                  />

                </div>
                {errors.subdomain && (
                  <p className="text-sm text-red-600">{errors.subdomain}</p>
                )}
                <p className="text-sm text-gray-500">
                  Chỉ sử dụng chữ cái thường, số và dấu gạch ngang
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

import { useCallback, useState } from 'react'
import { useRouter } from 'next/navigation'
import { toast } from 'sonner'
import { useCreateBookingPageV2Store } from '../stores/create-booking-page-v2.store'

interface CreateBookingPagePayload {
  domain: {
    subdomain: string
    customDomain?: string
  }
  template: {
    id: string
  }
  config: {
    bannerTitle: string
    bannerSubtitle: string
    bannerImage: string
    openTime: string
    closeTime: string
    fields: Array<{
      id: string
      name: string
      type: string
      capacity: number
    }>
  }
}

export const useCreateBookingPage = () => {
  const router = useRouter()
  const [isCreating, setIsCreating] = useState(false)
  
  const {
    domainConfig,
    selectedTemplateId,
    bookingConfig,
    validateCurrentStep,
    reset
  } = useCreateBookingPageV2Store()

  const createBookingPage = useCallback(async () => {
    // Validate all steps before creating
    if (!validateCurrentStep()) {
      toast.error('Vui lòng kiểm tra lại thông tin đã nhập')
      return false
    }

    setIsCreating(true)

    try {
      const payload: CreateBookingPagePayload = {
        domain: {
          subdomain: domainConfig.subdomain,
          customDomain: domainConfig.customDomain || undefined
        },
        template: {
          id: selectedTemplateId
        },
        config: {
          bannerTitle: bookingConfig.bannerTitle,
          bannerSubtitle: bookingConfig.bannerSubtitle,
          bannerImage: bookingConfig.bannerImage,
          openTime: bookingConfig.openTime,
          closeTime: bookingConfig.closeTime,
          fields: bookingConfig.fields.map(field => ({
            id: field.id,
            name: field.name,
            type: field.type,
            capacity: field.capacity
          }))
        }
      }

      // TODO: Replace with actual API call
      console.log('Creating booking page with payload:', payload)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Success
      toast.success('Trang đặt chỗ đã được tạo thành công!')
      
      // Reset form
      reset()
      
      // Redirect to booking page list or edit page
      router.push('/admin/booking-pages')
      
      return true
    } catch (error) {
      console.error('Error creating booking page:', error)
      toast.error('Có lỗi xảy ra khi tạo trang đặt chỗ. Vui lòng thử lại.')
      return false
    } finally {
      setIsCreating(false)
    }
  }, [
    domainConfig,
    selectedTemplateId,
    bookingConfig,
    validateCurrentStep,
    reset,
    router
  ])

  const saveDraft = useCallback(async () => {
    try {
      const draftData = {
        domainConfig,
        selectedTemplateId,
        bookingConfig,
        timestamp: new Date().toISOString()
      }

      // Save to localStorage as draft
      localStorage.setItem('booking-page-draft-v2', JSON.stringify(draftData))
      
      toast.success('Bản nháp đã được lưu')
      return true
    } catch (error) {
      console.error('Error saving draft:', error)
      toast.error('Không thể lưu bản nháp')
      return false
    }
  }, [domainConfig, selectedTemplateId, bookingConfig])

  const loadDraft = useCallback(() => {
    try {
      const draftData = localStorage.getItem('booking-page-draft-v2')
      if (draftData) {
        const parsed = JSON.parse(draftData)
        
        // TODO: Load draft data into store
        console.log('Loading draft:', parsed)
        
        toast.success('Đã tải bản nháp')
        return true
      }
      return false
    } catch (error) {
      console.error('Error loading draft:', error)
      toast.error('Không thể tải bản nháp')
      return false
    }
  }, [])

  const clearDraft = useCallback(() => {
    try {
      localStorage.removeItem('booking-page-draft-v2')
      return true
    } catch (error) {
      console.error('Error clearing draft:', error)
      return false
    }
  }, [])

  return {
    createBookingPage,
    saveDraft,
    loadDraft,
    clearDraft,
    isCreating
  }
}

import { useCallback } from 'react'
import { useCreateBookingPageV2Store } from '../stores/create-booking-page-v2.store'
import { validateStep } from '../utils/validation'

export const useStepValidation = () => {
  const {
    currentStep,
    domainConfig,
    selectedTemplateId,
    bookingConfig,
    errors
  } = useCreateBookingPageV2Store()

  const validateCurrentStep = useCallback(() => {
    let data: any
    switch (currentStep) {
      case 1:
        data = domainConfig
        break
      case 2:
        data = { selectedTemplateId }
        break
      case 3:
        data = bookingConfig
        break
      default:
        return { isValid: false, errors: {} }
    }

    return validateStep(currentStep, data)
  }, [currentStep, domainConfig, selectedTemplateId, bookingConfig])

  const getFieldError = useCallback((fieldName: string): string | undefined => {
    return errors[fieldName]
  }, [errors])

  const hasErrors = useCallback((): boolean => {
    return Object.values(errors).some(error => error !== '')
  }, [errors])

  const getStepProgress = useCallback(() => {
    const totalSteps = 3
    const completedSteps = currentStep - 1
    return Math.round((completedSteps / totalSteps) * 100)
  }, [currentStep])

  return {
    validateCurrentStep,
    getFieldError,
    hasErrors,
    getStepProgress,
    errors
  }
}

'use client'

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import {
  Check,
  ChevronLeft,
  ChevronRight,
  Clock,
  Globe,
  Image as ImageIcon,
  Palette,
  Plus,
  Settings,
  Trash2,
  Users,
} from 'lucide-react'
import React, { useState } from 'react'

// Types
interface DomainConfig {
  subdomain: string
  customDomain?: string
}

interface TemplateConfig {
  id: string
  name: string
  category: string
  preview: string
  description: string
}

interface BookingConfig {
  bannerTitle: string
  bannerSubtitle: string
  bannerImage: string
  openTime: string
  closeTime: string
  fields: Array<{
    id: string
    name: string
    type: 'football' | 'tennis' | 'badminton' | 'basketball'
    capacity: number
  }>
}

// Mock data
const TEMPLATES: TemplateConfig[] = [
  {
    id: 'sport-modern',
    name: '<PERSON><PERSON> thể thao hiện đại',
    category: 'Thể thao',
    preview: '/api/placeholder/300/200',
    description: '<PERSON>ia<PERSON> diện hiện đại cho đặt sân thể thao với lịch trực quan',
  },
  {
    id: 'sport-classic',
    name: 'Sân thể thao cổ điển',
    category: 'Thể thao',
    preview: '/api/placeholder/300/200',
    description: 'Thiết kế cổ điển, dễ sử dụng cho mọi lứa tuổi',
  },
  {
    id: 'sport-premium',
    name: 'Sân thể thao cao cấp',
    category: 'Thể thao',
    preview: '/api/placeholder/300/200',
    description: 'Giao diện sang trọng với nhiều tính năng nâng cao',
  },
  {
    id: 'event-simple',
    name: 'Sự kiện đơn giản',
    category: 'Sự kiện',
    preview: '/api/placeholder/300/200',
    description: 'Thiết kế tối giản cho đặt vé sự kiện',
  },
]

const CreateBookingPageV2Screen = () => {
  const [currentStep, setCurrentStep] = useState(1)
  const [domainConfig, setDomainConfig] = useState<DomainConfig>({
    subdomain: '',
  })
  const [selectedTemplate, setSelectedTemplate] = useState<string>('')
  const [bookingConfig, setBookingConfig] = useState<BookingConfig>({
    bannerTitle: '',
    bannerSubtitle: '',
    bannerImage: '',
    openTime: '06:00',
    closeTime: '22:00',
    fields: [],
  })

  const steps = [
    { id: 1, title: 'Tên miền', icon: Globe },
    { id: 2, title: 'Chọn mẫu', icon: Palette },
    { id: 3, title: 'Cấu hình', icon: Settings },
  ]

  const nextStep = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const addField = () => {
    const newField = {
      id: `field-${Date.now()}`,
      name: `Sân ${bookingConfig.fields.length + 1}`,
      type: 'football' as const,
      capacity: 1,
    }
    setBookingConfig(prev => ({
      ...prev,
      fields: [...prev.fields, newField],
    }))
  }

  const removeField = (fieldId: string) => {
    setBookingConfig(prev => ({
      ...prev,
      fields: prev.fields.filter(field => field.id !== fieldId),
    }))
  }

  const updateField = (fieldId: string, updates: Partial<BookingConfig['fields'][0]>) => {
    setBookingConfig(prev => ({
      ...prev,
      fields: prev.fields.map(field =>
        field.id === fieldId ? { ...field, ...updates } : field,
      ),
    }))
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-orange-100/30 to-orange-200/50">
      {/* Header */}
      <div className="bg-white/90 backdrop-blur-md border-b border-orange-200/50 sticky top-0 z-50 shadow-sm">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-4 lg:py-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div className="text-center lg:text-left">
              <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900">
                Tạo trang đặt chỗ mới
              </h1>
              <p className="text-sm sm:text-base text-gray-600 mt-1">
                Thiết lập trang đặt chỗ của bạn trong 3 bước đơn giản
              </p>
            </div>

            {/* Progress Steps - Desktop */}
            <div className="hidden lg:flex items-center space-x-4">
              {steps.map((step, index) => {
                const Icon = step.icon
                const isActive = currentStep === step.id
                const isCompleted = currentStep > step.id

                return (
                  <div key={step.id} className="flex items-center">
                    <div className={`
                      flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-300
                      ${isActive
                    ? 'bg-orange-500 border-orange-500 text-white shadow-lg'
                    : isCompleted
                      ? 'bg-green-500 border-green-500 text-white'
                      : 'bg-white border-gray-300 text-gray-400'
                  }
                    `}
                    >
                      {isCompleted
                        ? (
                            <Check className="w-5 h-5" />
                          )
                        : (
                            <Icon className="w-5 h-5" />
                          )}
                    </div>
                    <div className="ml-3">
                      <div className={`text-sm font-medium ${isActive ? 'text-orange-600' : isCompleted ? 'text-green-600' : 'text-gray-500'}`}>
                        Bước
                        {' '}
                        {step.id}
                      </div>
                      <div className={`text-xs ${isActive ? 'text-orange-500' : isCompleted ? 'text-green-500' : 'text-gray-400'}`}>
                        {step.title}
                      </div>
                    </div>
                    {index < steps.length - 1 && (
                      <div className={`w-8 h-0.5 mx-4 ${isCompleted ? 'bg-green-300' : 'bg-gray-200'}`} />
                    )}
                  </div>
                )
              })}
            </div>

            {/* Progress Steps - Mobile */}
            <div className="flex lg:hidden justify-center">
              <div className="flex items-center space-x-2">
                {steps.map((step, index) => {
                  const isActive = currentStep === step.id
                  const isCompleted = currentStep > step.id

                  return (
                    <div key={step.id} className="flex items-center">
                      <div className={`
                        w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium transition-all duration-300
                        ${isActive
                      ? 'bg-orange-500 text-white shadow-lg'
                      : isCompleted
                        ? 'bg-green-500 text-white'
                        : 'bg-gray-200 text-gray-500'
                    }
                      `}
                      >
                        {isCompleted ? <Check className="w-4 h-4" /> : step.id}
                      </div>
                      {index < steps.length - 1 && (
                        <div className={`w-4 h-0.5 mx-1 ${isCompleted ? 'bg-green-300' : 'bg-gray-200'}`} />
                      )}
                    </div>
                  )
                })}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6 lg:py-12">
        <div className="transition-all duration-300">
          {/* Step 1: Domain Configuration */}
          {currentStep === 1 && (
            <div className="animate-in fade-in slide-in-from-right-4 duration-300">

              <div className="max-w-4xl mx-auto">
                <Card className="bg-white/90 backdrop-blur-md border-orange-200/50 shadow-2xl">
                  <CardHeader className="text-center px-6 sm:px-8 lg:px-12 py-8">
                    <CardTitle className="text-2xl sm:text-3xl lg:text-4xl text-gray-900 flex items-center justify-center gap-3 mb-4">
                      <Globe className="w-7 h-7 lg:w-8 lg:h-8 text-orange-500" />
                      Chọn tên miền
                    </CardTitle>
                    <p className="text-base sm:text-lg text-gray-600 max-w-2xl mx-auto">
                      Chọn địa chỉ web để khách hàng có thể truy cập trang đặt chỗ của bạn
                    </p>
                  </CardHeader>
                  <CardContent className="px-6 sm:px-8 lg:px-12 pb-8 space-y-8">
                    <div className="space-y-6">
                      <div className="space-y-3">
                        <Label htmlFor="subdomain" className="text-base font-semibold text-gray-800">
                          Tên miền phụ *
                        </Label>
                        <div className="flex flex-col sm:flex-row sm:items-center gap-2">
                          <Input
                            id="subdomain"
                            value={domainConfig.subdomain}
                            onChange={e => setDomainConfig(prev => ({ ...prev, subdomain: e.target.value }))}
                            placeholder="ten-san-cua-ban"
                            className="flex-1 h-12 text-base border-orange-200 focus:border-orange-400 focus:ring-orange-400 focus:ring-2"
                          />
                          <span className="text-gray-600 font-medium text-base whitespace-nowrap">
                            .booking-easy.com
                          </span>
                        </div>
                        <p className="text-sm text-gray-500">
                          Chỉ sử dụng chữ cái, số và dấu gạch ngang
                        </p>
                      </div>

                      <div className="space-y-3">
                        <Label htmlFor="customDomain" className="text-base font-semibold text-gray-800">
                          Tên miền tùy chỉnh (tùy chọn)
                        </Label>
                        <Input
                          id="customDomain"
                          value={domainConfig.customDomain || ''}
                          onChange={e => setDomainConfig(prev => ({ ...prev, customDomain: e.target.value }))}
                          placeholder="www.tensancuaban.com"
                          className="h-12 text-base border-orange-200 focus:border-orange-400 focus:ring-orange-400 focus:ring-2"
                        />
                        <p className="text-sm text-gray-500">
                          Bạn có thể cấu hình tên miền riêng sau khi tạo trang
                        </p>
                      </div>
                    </div>

                    {domainConfig.subdomain && (
                      <div className="bg-gradient-to-r from-orange-50 to-orange-100 border border-orange-200 rounded-xl p-6">
                        <h4 className="font-semibold text-orange-800 mb-3 text-lg">Xem trước URL:</h4>
                        <div className="bg-white border border-orange-200 rounded-lg px-4 py-3 font-mono text-base text-orange-700 break-all">
                          https://
                          {domainConfig.subdomain}
                          .booking-easy.com
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          )}

          {/* Step 2: Template Selection */}
          {currentStep === 2 && (
            <div className="animate-in fade-in slide-in-from-right-4 duration-300">
              <div className="grid grid-cols-1 xl:grid-cols-2 gap-6 lg:gap-8">
                {/* Template List */}
                <Card className="bg-white/90 backdrop-blur-md border-orange-200/50 shadow-2xl">
                  <CardHeader className="px-6 py-6">
                    <CardTitle className="text-xl lg:text-2xl text-gray-900 flex items-center gap-3">
                      <Palette className="w-6 h-6 text-orange-500" />
                      Chọn mẫu giao diện
                    </CardTitle>
                    <p className="text-base text-gray-600 mt-2">
                      Chọn mẫu phù hợp với loại hình kinh doanh của bạn
                    </p>
                  </CardHeader>
                  <CardContent className="px-6 pb-6">
                    <div className="space-y-4">
                      {TEMPLATES.map(template => (
                        <div
                          key={template.id}
                          className={`
                            p-4 lg:p-5 border-2 rounded-xl cursor-pointer transition-all duration-300 hover:scale-[1.02]
                            ${selectedTemplate === template.id
                          ? 'border-orange-400 bg-gradient-to-r from-orange-50 to-orange-100 shadow-lg'
                          : 'border-gray-200 hover:border-orange-300 hover:bg-orange-25 hover:shadow-md'
                        }
                          `}
                          onClick={() => setSelectedTemplate(template.id)}
                        >
                          <div className="flex items-start gap-4">
                            <div className="w-20 h-14 lg:w-24 lg:h-16 bg-gray-200 rounded-lg overflow-hidden flex-shrink-0 shadow-sm">
                              <img
                                src={template.preview}
                                alt={template.name}
                                className="w-full h-full object-cover"
                              />
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-start justify-between gap-2 mb-2">
                                <div className="flex items-center gap-2 flex-wrap">
                                  <h3 className="font-semibold text-gray-900 text-base lg:text-lg">
                                    {template.name}
                                  </h3>
                                  <Badge variant="secondary" className="text-xs px-2 py-1">
                                    {template.category}
                                  </Badge>
                                </div>
                                {selectedTemplate === template.id && (
                                  <Check className="w-5 h-5 text-orange-500 flex-shrink-0" />
                                )}
                              </div>
                              <p className="text-sm lg:text-base text-gray-600 leading-relaxed">
                                {template.description}
                              </p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Template Preview */}
                <Card className="bg-white/90 backdrop-blur-md border-orange-200/50 shadow-2xl">
                  <CardHeader className="px-6 py-6">
                    <CardTitle className="text-xl lg:text-2xl text-gray-900">Xem trước giao diện</CardTitle>
                    <p className="text-base text-gray-600 mt-2">
                      {selectedTemplate
                        ? `Xem trước mẫu: ${TEMPLATES.find(t => t.id === selectedTemplate)?.name}`
                        : 'Chọn một mẫu để xem trước'}
                    </p>
                  </CardHeader>
                  <CardContent className="px-6 pb-6">
                    <div className="aspect-[4/3] bg-gray-100 rounded-xl border-2 border-dashed border-gray-300 flex items-center justify-center overflow-hidden">
                      {selectedTemplate
                        ? (
                            <div className="w-full h-full bg-white rounded-lg shadow-sm overflow-hidden">
                              <img
                                src={TEMPLATES.find(t => t.id === selectedTemplate)?.preview}
                                alt="Template preview"
                                className="w-full h-full object-cover"
                              />
                            </div>
                          )
                        : (
                            <div className="text-center text-gray-500">
                              <Palette className="w-16 h-16 mx-auto mb-4 opacity-50" />
                              <p className="text-lg font-medium">Chọn mẫu để xem trước</p>
                              <p className="text-sm mt-1">Giao diện sẽ hiển thị ở đây</p>
                            </div>
                          )}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          )}

          {/* Step 3: Booking Configuration */}
          {currentStep === 3 && (
            <div className="animate-in fade-in slide-in-from-right-4 duration-300">

              <Card className="max-w-4xl mx-auto bg-white/70 backdrop-blur-sm border-orange-200/50 shadow-xl">
                <CardHeader className="text-center">
                  <CardTitle className="text-2xl text-gray-900 flex items-center justify-center gap-2">
                    <Settings className="w-6 h-6 text-orange-500" />
                    Cấu hình trang đặt chỗ
                  </CardTitle>
                  <p className="text-gray-600 mt-2">
                    Thiết lập thông tin cơ bản để trang đặt chỗ có thể hoạt động
                  </p>
                </CardHeader>
                <CardContent className="space-y-8">
                  {/* Banner Configuration */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                      <ImageIcon className="w-5 h-5 text-orange-500" />
                      Thông tin banner
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="bannerTitle" className="text-sm font-medium text-gray-700">
                          Tiêu đề banner *
                        </Label>
                        <Input
                          id="bannerTitle"
                          value={bookingConfig.bannerTitle}
                          onChange={e => setBookingConfig(prev => ({ ...prev, bannerTitle: e.target.value }))}
                          placeholder="Đặt sân thể thao ABC"
                          className="mt-2 border-orange-200 focus:border-orange-400 focus:ring-orange-400"
                        />
                      </div>
                      <div>
                        <Label htmlFor="bannerSubtitle" className="text-sm font-medium text-gray-700">
                          Mô tả ngắn
                        </Label>
                        <Input
                          id="bannerSubtitle"
                          value={bookingConfig.bannerSubtitle}
                          onChange={e => setBookingConfig(prev => ({ ...prev, bannerSubtitle: e.target.value }))}
                          placeholder="Chọn sân và thời gian phù hợp với bạn"
                          className="mt-2 border-orange-200 focus:border-orange-400 focus:ring-orange-400"
                        />
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="bannerImage" className="text-sm font-medium text-gray-700">
                        URL hình ảnh banner
                      </Label>
                      <Input
                        id="bannerImage"
                        value={bookingConfig.bannerImage}
                        onChange={e => setBookingConfig(prev => ({ ...prev, bannerImage: e.target.value }))}
                        placeholder="https://example.com/banner.jpg"
                        className="mt-2 border-orange-200 focus:border-orange-400 focus:ring-orange-400"
                      />
                    </div>
                  </div>

                  <Separator className="bg-orange-200" />

                  {/* Operating Hours */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                      <Clock className="w-5 h-5 text-orange-500" />
                      Giờ hoạt động
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="openTime" className="text-sm font-medium text-gray-700">
                          Giờ mở cửa *
                        </Label>
                        <Input
                          id="openTime"
                          type="time"
                          value={bookingConfig.openTime}
                          onChange={e => setBookingConfig(prev => ({ ...prev, openTime: e.target.value }))}
                          className="mt-2 border-orange-200 focus:border-orange-400 focus:ring-orange-400"
                        />
                      </div>
                      <div>
                        <Label htmlFor="closeTime" className="text-sm font-medium text-gray-700">
                          Giờ đóng cửa *
                        </Label>
                        <Input
                          id="closeTime"
                          type="time"
                          value={bookingConfig.closeTime}
                          onChange={e => setBookingConfig(prev => ({ ...prev, closeTime: e.target.value }))}
                          className="mt-2 border-orange-200 focus:border-orange-400 focus:ring-orange-400"
                        />
                      </div>
                    </div>
                  </div>

                  <Separator className="bg-orange-200" />

                  {/* Fields Configuration */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                        <Users className="w-5 h-5 text-orange-500" />
                        Danh sách sân (
                        {bookingConfig.fields.length}
                        )
                      </h3>
                      <Button
                        onClick={addField}
                        variant="outline"
                        size="sm"
                        className="border-orange-300 text-orange-600 hover:bg-orange-50"
                      >
                        <Plus className="w-4 h-4 mr-2" />
                        Thêm sân
                      </Button>
                    </div>

                    {bookingConfig.fields.length === 0
                      ? (
                          <div className="text-center py-8 text-gray-500">
                            <Users className="w-12 h-12 mx-auto mb-2 opacity-50" />
                            <p>Chưa có sân nào. Nhấn "Thêm sân" để bắt đầu.</p>
                          </div>
                        )
                      : (
                          <div className="space-y-3">
                            {bookingConfig.fields.map((field, index) => (
                              <div
                                key={field.id}
                                className="p-4 border border-orange-200 rounded-lg bg-orange-25"
                              >
                                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                                  <div>
                                    <Label className="text-sm font-medium text-gray-700">
                                      Tên sân *
                                    </Label>
                                    <Input
                                      value={field.name}
                                      onChange={e => updateField(field.id, { name: e.target.value })}
                                      placeholder={`Sân ${index + 1}`}
                                      className="mt-1 border-orange-200 focus:border-orange-400 focus:ring-orange-400"
                                    />
                                  </div>
                                  <div>
                                    <Label className="text-sm font-medium text-gray-700">
                                      Loại sân
                                    </Label>
                                    <select
                                      value={field.type}
                                      onChange={e => updateField(field.id, { type: e.target.value as any })}
                                      className="mt-1 w-full px-3 py-2 border border-orange-200 rounded-md focus:border-orange-400 focus:ring-orange-400"
                                    >
                                      <option value="football">Bóng đá</option>
                                      <option value="tennis">Tennis</option>
                                      <option value="badminton">Cầu lông</option>
                                      <option value="basketball">Bóng rổ</option>
                                    </select>
                                  </div>
                                  <div>
                                    <Label className="text-sm font-medium text-gray-700">
                                      Sức chứa
                                    </Label>
                                    <Input
                                      type="number"
                                      min="1"
                                      value={field.capacity}
                                      onChange={e => updateField(field.id, { capacity: Number.parseInt(e.target.value) })}
                                      className="mt-1 border-orange-200 focus:border-orange-400 focus:ring-orange-400"
                                    />
                                  </div>
                                  <div>
                                    <Button
                                      onClick={() => removeField(field.id)}
                                      variant="outline"
                                      size="sm"
                                      className="border-red-300 text-red-600 hover:bg-red-50"
                                    >
                                      <Trash2 className="w-4 h-4" />
                                    </Button>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </div>

        {/* Navigation Footer */}
        <div className="mt-8 lg:mt-12">
          <div className="bg-white/90 backdrop-blur-md border border-orange-200/50 rounded-2xl p-6 shadow-xl">
            <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
              <Button
                onClick={prevStep}
                disabled={currentStep === 1}
                variant="outline"
                size="lg"
                className="w-full sm:w-auto border-orange-300 text-orange-600 hover:bg-orange-50 disabled:opacity-50 disabled:cursor-not-allowed h-12 px-6"
              >
                <ChevronLeft className="w-5 h-5 mr-2" />
                Quay lại
              </Button>

              {/* Progress Dots */}
              <div className="flex items-center gap-3">
                {steps.map(step => (
                  <div
                    key={step.id}
                    className={`rounded-full transition-all duration-300 ${
                      currentStep === step.id
                        ? 'bg-orange-500 w-10 h-3'
                        : currentStep > step.id
                          ? 'bg-green-500 w-3 h-3'
                          : 'bg-gray-300 w-3 h-3'
                    }`}
                  />
                ))}
              </div>

              {currentStep < 3
                ? (
                    <Button
                      onClick={nextStep}
                      disabled={
                        (currentStep === 1 && !domainConfig.subdomain)
                        || (currentStep === 2 && !selectedTemplate)
                      }
                      size="lg"
                      className="w-full sm:w-auto bg-orange-500 hover:bg-orange-600 text-white disabled:opacity-50 disabled:cursor-not-allowed h-12 px-6 shadow-lg"
                    >
                      Tiếp theo
                      <ChevronRight className="w-5 h-5 ml-2" />
                    </Button>
                  )
                : (
                    <Button
                      disabled={
                        !bookingConfig.bannerTitle
                        || !bookingConfig.openTime
                        || !bookingConfig.closeTime
                        || bookingConfig.fields.length === 0
                      }
                      size="lg"
                      className="w-full sm:w-auto bg-green-500 hover:bg-green-600 text-white disabled:opacity-50 disabled:cursor-not-allowed h-12 px-6 shadow-lg"
                    >
                      <Check className="w-5 h-5 mr-2" />
                      Tạo trang đặt chỗ
                    </Button>
                  )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CreateBookingPageV2Screen

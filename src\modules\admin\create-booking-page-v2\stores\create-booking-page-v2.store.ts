import type { BookingConfig, BookingField, CreateBookingPageV2State, DomainConfig } from '../types'
import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { validateStep } from '../utils/validation'

const initialDomainConfig: DomainConfig = {
  subdomain: '',
  customDomain: '',
}

const initialBookingConfig: BookingConfig = {
  bannerTitle: '',
  bannerSubtitle: '',
  bannerImage: '',
  openTime: '06:00',
  closeTime: '22:00',
  fields: [],
}

export const useCreateBookingPageV2Store = create<CreateBookingPageV2State>()(
  devtools(
    (set, get) => ({
      // State
      currentStep: 1,
      domainConfig: initialDomainConfig,
      selectedTemplateId: '',
      bookingConfig: initialBookingConfig,
      isLoading: false,
      errors: {},

      // Step navigation
      setCurrentStep: (step: number) => {
        set({ currentStep: step }, false, 'setCurrentStep')
      },

      nextStep: () => {
        const { currentStep, validateCurrentStep } = get()
        if (validateCurrentStep() && currentStep < 3) {
          set({ currentStep: currentStep + 1, errors: {} }, false, 'nextStep')
        }
      },

      prevStep: () => {
        const { currentStep } = get()
        if (currentStep > 1) {
          set({ currentStep: currentStep - 1, errors: {} }, false, 'prevStep')
        }
      },

      // Domain config actions
      updateDomainConfig: (config: Partial<DomainConfig>) => {
        set(
          state => ({
            domainConfig: { ...state.domainConfig, ...config },
            errors: { ...state.errors, subdomain: '', customDomain: '' },
          }),
          false,
          'updateDomainConfig',
        )
      },

      // Template actions
      setSelectedTemplate: (templateId: string) => {
        set(
          { selectedTemplateId: templateId, errors: { ...get().errors, selectedTemplateId: '' } },
          false,
          'setSelectedTemplate',
        )
      },

      // Booking config actions
      updateBookingConfig: (config: Partial<BookingConfig>) => {
        set(
          state => ({
            bookingConfig: { ...state.bookingConfig, ...config },
            errors: { ...state.errors, ...Object.keys(config).reduce((acc, key) => ({ ...acc, [key]: '' }), {}) },
          }),
          false,
          'updateBookingConfig',
        )
      },

      addField: () => {
        const { bookingConfig } = get()
        const newField: BookingField = {
          id: `field-${Date.now()}`,
          name: `Sân ${bookingConfig.fields.length + 1}`,
          type: 'football',
          capacity: 1,
        }

        set(
          state => ({
            bookingConfig: {
              ...state.bookingConfig,
              fields: [...state.bookingConfig.fields, newField],
            },
            errors: { ...state.errors, fields: '' },
          }),
          false,
          'addField',
        )
      },

      removeField: (fieldId: string) => {
        set(
          state => ({
            bookingConfig: {
              ...state.bookingConfig,
              fields: state.bookingConfig.fields.filter(field => field.id !== fieldId),
            },
          }),
          false,
          'removeField',
        )
      },

      updateField: (fieldId: string, updates: Partial<BookingField>) => {
        set(
          state => ({
            bookingConfig: {
              ...state.bookingConfig,
              fields: state.bookingConfig.fields.map(field =>
                field.id === fieldId ? { ...field, ...updates } : field,
              ),
            },
          }),
          false,
          'updateField',
        )
      },

      // Validation
      validateCurrentStep: () => {
        const { currentStep, domainConfig, selectedTemplateId, bookingConfig } = get()

        let data: any
        switch (currentStep) {
          case 1:
            data = domainConfig
            break
          case 2:
            data = { selectedTemplateId }
            break
          case 3:
            data = bookingConfig
            break
          default:
            return false
        }

        const { isValid, errors } = validateStep(currentStep, data)

        if (!isValid) {
          set({ errors }, false, 'validateCurrentStep')
        }

        return isValid
      },

      getStepErrors: (step: number) => {
        const { domainConfig, selectedTemplateId, bookingConfig } = get()

        let data: any
        switch (step) {
          case 1:
            data = domainConfig
            break
          case 2:
            data = { selectedTemplateId }
            break
          case 3:
            data = bookingConfig
            break
          default:
            return []
        }

        const { errors } = validateStep(step, data)
        return Object.values(errors).filter(Boolean) as string[]
      },

      // Reset
      reset: () => {
        set(
          {
            currentStep: 1,
            domainConfig: initialDomainConfig,
            selectedTemplateId: '',
            bookingConfig: initialBookingConfig,
            isLoading: false,
            errors: {},
          },
          false,
          'reset',
        )
      },
    }),
    {
      name: 'create-booking-page-v2-store',
    },
  ),
)
